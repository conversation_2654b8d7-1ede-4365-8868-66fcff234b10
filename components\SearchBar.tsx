import React, { useState, useRef, useEffect } from 'react';
import { MagnifyingGlassIcon, XMarkIcon } from '../constants';

interface SearchResult {
  id: string;
  title: string;
  type: 'category' | 'subcategory' | 'action';
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface SearchBarProps {
  onFocus?: () => void;
  onBlur?: () => void;
  isFocused?: boolean;
  placeholder?: string;
  categories?: any[];
  transactions?: any[];
}

const SearchBar: React.FC<SearchBarProps> = ({
  onFocus,
  onBlur,
  isFocused = false,
  placeholder = "Search categories, transactions...",
  categories = [],
  transactions = []
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Generate search results from real data
  const generateSearchResults = (searchQuery: string): SearchResult[] => {
    const results: SearchResult[] = [];
    const lowerQuery = searchQuery.toLowerCase();

    // Add categories
    categories.forEach(category => {
      if (category.name.toLowerCase().includes(lowerQuery)) {
        results.push({
          id: `category-${category.id}`,
          title: category.name,
          type: 'category',
          description: `Budget: ${category.allocatedAmount || 0} | Spent: ${category.spentAmount || 0}`
        });
      }

      // Add subcategories
      category.subcategories?.forEach((subcategory: any) => {
        if (subcategory.name.toLowerCase().includes(lowerQuery)) {
          results.push({
            id: `subcategory-${subcategory.id}`,
            title: subcategory.name,
            type: 'subcategory',
            description: `Under ${category.name} | Budget: ${subcategory.allocatedAmount || 0}`
          });
        }
      });
    });

    // Add recent transactions (limit to 5)
    const recentTransactions = transactions
      .filter((transaction: any) =>
        transaction.description.toLowerCase().includes(lowerQuery) ||
        categories.find(cat => cat.id === transaction.categoryId)?.name.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 5);

    recentTransactions.forEach((transaction: any) => {
      const category = categories.find(cat => cat.id === transaction.categoryId);
      results.push({
        id: `transaction-${transaction.id}`,
        title: transaction.description,
        type: 'action',
        description: `${transaction.type === 'expense' ? '-' : '+'}${transaction.amount} in ${category?.name || 'Unknown'}`
      });
    });

    // Add action items
    if (lowerQuery.includes('add') || lowerQuery.includes('new') || lowerQuery.includes('create')) {
      results.push({
        id: 'action-add-category',
        title: 'Add New Category',
        type: 'action',
        description: 'Create a new budget category'
      });
    }

    return results.slice(0, 8); // Limit total results
  };

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter results based on query
  useEffect(() => {
    if (query.trim()) {
      const searchResults = generateSearchResults(query.trim());
      setResults(searchResults);
      setIsOpen(true);
    } else {
      setResults([]);
      setIsOpen(false);
    }
  }, [query, categories, transactions]);

  const handleInputFocus = () => {
    onFocus?.();
    if (query.trim()) {
      setIsOpen(true);
    }
  };

  const handleInputBlur = () => {
    onBlur?.();
    // Delay closing to allow for result clicks
    setTimeout(() => setIsOpen(false), 150);
  };

  const handleResultClick = (result: SearchResult) => {
    setQuery(result.title);
    setIsOpen(false);
    inputRef.current?.blur();
    
    // Handle different result types
    switch (result.type) {
      case 'action':
        if (result.title === 'Add New Category') {
          // Trigger add category action
          console.log('Add new category triggered');
        }
        break;
      case 'category':
      case 'subcategory':
        // Navigate to or highlight the category/subcategory
        console.log(`Navigate to ${result.type}: ${result.title}`);
        break;
    }
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'category':
        return '📁';
      case 'subcategory':
        return '📄';
      case 'action':
        return '⚡';
      default:
        return '🔍';
    }
  };

  const getResultTypeColor = (type: string) => {
    switch (type) {
      case 'category':
        return 'text-sky-400';
      case 'subcategory':
        return 'text-green-400';
      case 'action':
        return 'text-purple-400';
      default:
        return 'text-slate-400';
    }
  };

  return (
    <div className="relative w-full" ref={searchRef}>
      {/* Search Input */}
      <div className={`relative transition-all duration-200 ${
        isFocused ? 'transform scale-105' : ''
      }`}>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-slate-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-200"
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-white transition-colors duration-200"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && results.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-slate-800 rounded-lg shadow-xl border border-slate-700/50 py-2 z-50 max-h-80 overflow-y-auto">
          {results.map((result) => (
            <button
              key={result.id}
              onClick={() => handleResultClick(result)}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-slate-700/50 transition-colors duration-200 group"
            >
              <span className="text-lg">{getResultIcon(result.type)}</span>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-white font-medium text-sm truncate group-hover:text-sky-400 transition-colors duration-200">
                    {result.title}
                  </p>
                  <span className={`text-xs px-2 py-0.5 rounded-full bg-slate-700 ${getResultTypeColor(result.type)}`}>
                    {result.type}
                  </span>
                </div>
                {result.description && (
                  <p className="text-slate-400 text-xs truncate mt-0.5">
                    {result.description}
                  </p>
                )}
              </div>
            </button>
          ))}
          
          {/* Search Footer */}
          <div className="border-t border-slate-700/50 mt-2 pt-2 px-4">
            <p className="text-xs text-slate-500">
              Press <kbd className="px-1.5 py-0.5 bg-slate-700 rounded text-slate-300">Enter</kbd> to search or <kbd className="px-1.5 py-0.5 bg-slate-700 rounded text-slate-300">Esc</kbd> to close
            </p>
          </div>
        </div>
      )}

      {/* No Results */}
      {isOpen && query.trim() && results.length === 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-slate-800 rounded-lg shadow-xl border border-slate-700/50 py-8 z-50">
          <div className="text-center">
            <MagnifyingGlassIcon className="h-8 w-8 text-slate-500 mx-auto mb-2" />
            <p className="text-slate-400 text-sm">No results found for "{query}"</p>
            <p className="text-slate-500 text-xs mt-1">Try searching for categories, subcategories, or actions</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
